#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار قوائم السياق للبطاقات والجداول
"""

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QTableWidget, QTableWidgetItem, QPushButton,
                               QLabel, QMessageBox)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# استيراد الكلاسات المحدثة
try:
    from نظام_البطاقات import ModernCard
    from ui_boton import MainWindow
    from مراحل_المشروع import ProjectPhasesWindow
except ImportError as e:
    print(f"خطأ في استيراد الملفات: {e}")

class TestContextMenusWindow(QMainWindow):
    """نافذة اختبار قوائم السياق"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار قوائم السياق - Cards & Tables")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الخط العربي
        font = QFont("Arial", 12)
        self.setFont(font)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # عنوان الاختبار
        title_label = QLabel("اختبار قوائم السياق للبطاقات والجداول")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # قسم البطاقات
        cards_section = self.create_cards_section()
        main_layout.addWidget(cards_section)
        
        # قسم الجداول
        tables_section = self.create_tables_section()
        main_layout.addWidget(tables_section)
        
        # أزرار الاختبار
        buttons_layout = QHBoxLayout()
        
        test_cards_btn = QPushButton("اختبار البطاقات")
        test_cards_btn.clicked.connect(self.test_cards)
        buttons_layout.addWidget(test_cards_btn)
        
        test_tables_btn = QPushButton("اختبار الجداول")
        test_tables_btn.clicked.connect(self.test_tables)
        buttons_layout.addWidget(test_tables_btn)
        
        open_main_app_btn = QPushButton("فتح التطبيق الرئيسي")
        open_main_app_btn.clicked.connect(self.open_main_app)
        buttons_layout.addWidget(open_main_app_btn)
        
        main_layout.addLayout(buttons_layout)
        
    def create_cards_section(self):
        """إنشاء قسم اختبار البطاقات"""
        cards_widget = QWidget()
        cards_layout = QVBoxLayout(cards_widget)
        
        # عنوان القسم
        section_title = QLabel("اختبار البطاقات (Cards)")
        section_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #34495e;")
        cards_layout.addWidget(section_title)
        
        # حاوية البطاقات
        cards_container = QWidget()
        cards_container_layout = QHBoxLayout(cards_container)
        
        # بيانات تجريبية للبطاقات
        sample_data = [
            {
                'اسم_المشروع': 'مشروع تجريبي 1',
                'اسم_العميل': 'عميل تجريبي 1',
                'حالة_المشروع': 'قيد التنفيذ',
                'id': 1
            },
            {
                'اسم_الموظف': 'موظف تجريبي',
                'الوظيفة': 'مهندس',
                'القسم': 'الهندسة',
                'id': 2
            },
            {
                'البيان': 'حساب تجريبي',
                'النوع': 'مصروف',
                'المبلغ': '1000',
                'id': 3
            }
        ]
        
        # إنشاء البطاقات
        card_types = ['project', 'employee', 'expense']
        for i, (data, card_type) in enumerate(zip(sample_data, card_types)):
            try:
                card = ModernCard(data, card_type, self)
                card.card_clicked.connect(self.on_card_clicked)
                card.card_double_clicked.connect(self.on_card_double_clicked)
                cards_container_layout.addWidget(card)
            except Exception as e:
                error_label = QLabel(f"خطأ في إنشاء البطاقة {i+1}: {str(e)}")
                error_label.setStyleSheet("color: red;")
                cards_container_layout.addWidget(error_label)
        
        cards_layout.addWidget(cards_container)
        
        # تعليمات الاستخدام
        instructions = QLabel("تعليمات: اضغط بالزر الأيمن على أي بطاقة لعرض قائمة السياق")
        instructions.setStyleSheet("color: #7f8c8d; font-style: italic;")
        cards_layout.addWidget(instructions)
        
        return cards_widget
        
    def create_tables_section(self):
        """إنشاء قسم اختبار الجداول"""
        tables_widget = QWidget()
        tables_layout = QVBoxLayout(tables_widget)
        
        # عنوان القسم
        section_title = QLabel("اختبار الجداول (Tables)")
        section_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #34495e;")
        tables_layout.addWidget(section_title)
        
        # إنشاء جدول تجريبي
        test_table = QTableWidget()
        test_table.setRowCount(5)
        test_table.setColumnCount(4)
        test_table.setHorizontalHeaderLabels(['الرقم', 'الاسم', 'النوع', 'الحالة'])
        
        # إضافة بيانات تجريبية
        sample_table_data = [
            ['1', 'عنصر تجريبي 1', 'نوع أ', 'نشط'],
            ['2', 'عنصر تجريبي 2', 'نوع ب', 'غير نشط'],
            ['3', 'عنصر تجريبي 3', 'نوع ج', 'نشط'],
            ['4', 'عنصر تجريبي 4', 'نوع د', 'قيد المراجعة'],
            ['5', 'عنصر تجريبي 5', 'نوع هـ', 'نشط']
        ]
        
        for row, row_data in enumerate(sample_table_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                item.setTextAlignment(Qt.AlignCenter)
                test_table.setItem(row, col, item)
        
        # إضافة قائمة السياق للجدول
        test_table.setContextMenuPolicy(Qt.CustomContextMenu)
        test_table.customContextMenuRequested.connect(self.show_table_context_menu)
        
        tables_layout.addWidget(test_table)
        
        # تعليمات الاستخدام
        instructions = QLabel("تعليمات: اضغط بالزر الأيمن على أي صف في الجدول لعرض قائمة السياق")
        instructions.setStyleSheet("color: #7f8c8d; font-style: italic;")
        tables_layout.addWidget(instructions)
        
        return tables_widget
    
    def show_table_context_menu(self, position):
        """عرض قائمة السياق للجدول التجريبي"""
        table = self.sender()
        current_row = table.currentRow()
        if current_row < 0:
            return
        
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)
        
        # إضافة الإجراءات
        view_action = context_menu.addAction("👁️ عرض")
        view_action.triggered.connect(lambda: self.handle_table_action("عرض", current_row))
        
        edit_action = context_menu.addAction("✏️ تعديل")
        edit_action.triggered.connect(lambda: self.handle_table_action("تعديل", current_row))
        
        delete_action = context_menu.addAction("🗑️ حذف")
        delete_action.triggered.connect(lambda: self.handle_table_action("حذف", current_row))
        
        # تطبيق الستايل
        context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 5px;
                font-family: 'Arial', sans-serif;
                font-size: 12px;
                direction: rtl;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 20px;
                margin: 2px;
                border-radius: 4px;
                text-align: right;
            }
            QMenu::item:selected {
                background-color: #3498db;
                color: white;
            }
            QMenu::item:pressed {
                background-color: #2980b9;
            }
        """)
        
        context_menu.exec(table.mapToGlobal(position))
    
    def handle_table_action(self, action, row):
        """معالجة إجراءات قائمة السياق للجدول"""
        QMessageBox.information(self, "إجراء الجدول", f"تم تنفيذ إجراء '{action}' على الصف {row + 1}")
    
    def on_card_clicked(self, data):
        """معالجة النقر على البطاقة"""
        QMessageBox.information(self, "نقر البطاقة", f"تم النقر على البطاقة: {data}")
    
    def on_card_double_clicked(self, data):
        """معالجة النقر المزدوج على البطاقة"""
        QMessageBox.information(self, "نقر مزدوج على البطاقة", f"تم النقر المزدوج على البطاقة: {data}")
    
    def test_cards(self):
        """اختبار البطاقات"""
        QMessageBox.information(self, "اختبار البطاقات", 
                               "لاختبار البطاقات:\n"
                               "1. اضغط بالزر الأيمن على أي بطاقة\n"
                               "2. ستظهر قائمة السياق مع خيارات: عرض، تعديل، حذف\n"
                               "3. اختر أي خيار لتجربة الوظيفة")
    
    def test_tables(self):
        """اختبار الجداول"""
        QMessageBox.information(self, "اختبار الجداول", 
                               "لاختبار الجداول:\n"
                               "1. اضغط بالزر الأيمن على أي صف في الجدول\n"
                               "2. ستظهر قائمة السياق مع خيارات: عرض، تعديل، حذف\n"
                               "3. اختر أي خيار لتجربة الوظيفة")
    
    def open_main_app(self):
        """فتح التطبيق الرئيسي"""
        try:
            self.main_window = MainWindow()
            self.main_window.show()
            QMessageBox.information(self, "التطبيق الرئيسي", "تم فتح التطبيق الرئيسي بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح التطبيق الرئيسي: {str(e)}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي للتطبيق
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء وعرض النافذة
    window = TestContextMenusWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
