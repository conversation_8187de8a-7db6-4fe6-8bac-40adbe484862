#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify that payment insertion works correctly
after fixing the trigger issues.
"""

import mysql.connector
from datetime import date

def test_payment_insertion():
    """Test payment insertion to verify the fix"""
    
    try:
        # Import database connection details
        from for_all import host, user, password
        
        # Connect to database
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        print("🔗 Connected to database successfully")
        
        # First, check if we have any projects to test with
        cursor.execute("SELECT id, اسم_المشروع, معرف_العميل FROM المشاريع LIMIT 1")
        project = cursor.fetchone()
        
        if not project:
            print("❌ No projects found in database - cannot test payment insertion")
            return False
            
        project_id, project_name, client_id = project
        print(f"📋 Found test project: ID={project_id}, Name={project_name}, Client ID={client_id}")
        
        # Test payment insertion
        test_payment_data = {
            'معرف_العميل': client_id,
            'معرف_المشروع': project_id,
            'المبلغ_المدفوع': 500.00,
            'وصف_المدفوع': 'دفعة تجريبية لاختبار النظام',
            'تاريخ_الدفع': date.today(),
            'طريقة_الدفع': 'نقدي',
            'خصم': 0,
            'المستلم': 'اختبار النظام',
            'المستخدم': 'النظام'
        }
        
        print("💰 Attempting to insert test payment...")
        
        cursor.execute("""
            INSERT INTO المشاريع_المدفوعات 
            (معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, خصم, المستلم, المستخدم) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            test_payment_data['معرف_العميل'],
            test_payment_data['معرف_المشروع'],
            test_payment_data['المبلغ_المدفوع'],
            test_payment_data['وصف_المدفوع'],
            test_payment_data['تاريخ_الدفع'],
            test_payment_data['طريقة_الدفع'],
            test_payment_data['خصم'],
            test_payment_data['المستلم'],
            test_payment_data['المستخدم']
        ))
        
        payment_id = cursor.lastrowid
        print(f"✅ Test payment inserted successfully with ID: {payment_id}")
        
        # Verify the payment was inserted correctly
        cursor.execute("""
            SELECT dp.id, dp.المبلغ_المدفوع, dp.وصف_المدفوع, c.اسم_العميل, p.اسم_المشروع
            FROM المشاريع_المدفوعات dp
            LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
            LEFT JOIN العملاء c ON p.معرف_العميل = c.id
            WHERE dp.id = %s
        """, (payment_id,))
        
        payment_details = cursor.fetchone()
        if payment_details:
            pid, amount, description, client_name, project_name = payment_details
            print(f"📋 Payment details:")
            print(f"   ID: {pid}")
            print(f"   Amount: {amount}")
            print(f"   Description: {description}")
            print(f"   Client: {client_name or 'غير محدد'}")
            print(f"   Project: {project_name or 'غير محدد'}")
        
        # Check if the project's paid amount was updated by the trigger
        cursor.execute("SELECT المدفوع FROM المشاريع WHERE id = %s", (project_id,))
        paid_amount = cursor.fetchone()[0]
        print(f"💰 Project paid amount after insertion: {paid_amount}")
        
        # Clean up test payment
        cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (payment_id,))
        print("🧹 Test payment cleaned up")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 Payment insertion test completed successfully!")
        print("   The 'Unknown column اسم_العميل' error has been fixed!")
        return True
        
    except ImportError:
        print("❌ Error: Could not import database connection details from for_all.py")
        return False
        
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
        if "Unknown column" in str(e):
            print("   The column reference error still exists - check triggers and table schemas")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Payment Insertion Test")
    print("=" * 50)
    success = test_payment_insertion()
    
    if success:
        print("\n✅ Test passed - you can now use the payment functionality in دفعات_المشروع.py")
    else:
        print("\n❌ Test failed - there may still be issues to resolve")
