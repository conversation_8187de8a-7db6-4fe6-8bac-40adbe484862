#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script to fix the MySQL trigger error that's causing 'Unknown column اسم_العميل' 
when saving payments in دفعات_المشروع.py

The issue is that the trigger 'تحديث_التصميم' is trying to update a non-existent 
column 'اسم_العميل' in the المشاريع_المراحل table.
"""

import mysql.connector
import sys

def fix_trigger_error():
    """Fix the problematic trigger that references non-existent اسم_العميل column"""
    
    try:
        # Import database connection details
        from for_all import host, user, password
        
        # Connect to database
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        print("🔗 Connected to database successfully")
        
        # First, check if the problematic trigger exists
        cursor.execute("""
            SELECT TRIGGER_NAME 
            FROM INFORMATION_SCHEMA.TRIGGERS 
            WHERE TRIGGER_SCHEMA = 'project_manager_V2' 
            AND TRIGGER_NAME = 'تحديث_التصميم'
        """)
        
        if cursor.fetchone():
            print("⚠️  Found problematic trigger 'تحديث_التصميم'")
            
            # Drop the problematic trigger
            cursor.execute("DROP TRIGGER IF EXISTS تحديث_التصميم")
            print("✅ Dropped problematic trigger 'تحديث_التصميم'")
            
            # Check if المشاريع_المراحل table has اسم_العميل column
            cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'project_manager_V2' 
                AND TABLE_NAME = 'المشاريع_المراحل' 
                AND COLUMN_NAME = 'اسم_العميل'
            """)
            
            has_client_name_column = cursor.fetchone() is not None
            
            if has_client_name_column:
                print("📋 المشاريع_المراحل table has اسم_العميل column - recreating trigger")
                # Recreate the trigger (it should work if the column exists)
                new_trigger = """
                CREATE TRIGGER تحديث_التصميم
                AFTER UPDATE ON المشاريع
                FOR EACH ROW
                BEGIN
                    DECLARE client_name VARCHAR(255);

                    -- Get client name from the clients table
                    SELECT اسم_العميل INTO client_name
                    FROM العملاء
                    WHERE id = NEW.معرف_العميل;

                    UPDATE المشاريع_المراحل
                    SET
                        اسم_العميل = client_name,
                        اسم_المشروع = NEW.اسم_المشروع
                    WHERE معرف_العميل = NEW.id;
                END
                """
                cursor.execute(new_trigger)
                print("✅ Recreated trigger with proper column references")
            else:
                print("📋 المشاريع_المراحل table does NOT have اسم_العميل column")
                print("🔧 Creating new trigger without اسم_العميل reference")
                # Check if المشاريع_المراحل table has اسم_المشروع column
                cursor.execute("""
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'project_manager_V2'
                    AND TABLE_NAME = 'المشاريع_المراحل'
                    AND COLUMN_NAME = 'اسم_المشروع'
                """)

                has_project_name_column = cursor.fetchone() is not None

                if has_project_name_column:
                    # Create a new trigger with اسم_المشروع only
                    new_trigger = """
                    CREATE TRIGGER تحديث_التصميم
                    AFTER UPDATE ON المشاريع
                    FOR EACH ROW
                    BEGIN
                        UPDATE المشاريع_المراحل
                        SET اسم_المشروع = NEW.اسم_المشروع
                        WHERE معرف_المشروع = NEW.id;
                    END
                    """
                    cursor.execute(new_trigger)
                    print("✅ Created new trigger with اسم_المشروع column only")
                else:
                    print("📋 المشاريع_المراحل table does NOT have اسم_المشروع column either")
                    print("🔧 Skipping trigger creation - no relevant columns to update")
                    print("✅ Trigger 'تحديث_التصميم' removed completely")
        else:
            print("ℹ️  Trigger 'تحديث_التصميم' not found - no action needed")
        
        # Also check and fix the custody trigger if needed
        cursor.execute("""
            SELECT TRIGGER_NAME
            FROM INFORMATION_SCHEMA.TRIGGERS
            WHERE TRIGGER_SCHEMA = 'project_manager_V2'
            AND TRIGGER_NAME = 'update_custody_project_info'
        """)

        if cursor.fetchone():
            print("⚠️  Found trigger 'update_custody_project_info'")

            # Check if المقاولات_العهد table has اسم_العميل and اسم_المشروع columns
            cursor.execute("""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'project_manager_V2'
                AND TABLE_NAME = 'المقاولات_العهد'
                AND COLUMN_NAME IN ('اسم_العميل', 'اسم_المشروع')
            """)

            existing_columns = [row[0] for row in cursor.fetchall()]
            has_client_column = 'اسم_العميل' in existing_columns
            has_project_column = 'اسم_المشروع' in existing_columns

            print(f"📋 المقاولات_العهد table columns: اسم_العميل={has_client_column}, اسم_المشروع={has_project_column}")

            if not has_client_column and not has_project_column:
                print("🔧 Both columns missing - dropping trigger completely")
                cursor.execute("DROP TRIGGER IF EXISTS update_custody_project_info")
                print("✅ Dropped custody trigger (no relevant columns to update)")
            elif not has_client_column or not has_project_column:
                print("🔧 Some columns missing - recreating trigger with existing columns only")
                cursor.execute("DROP TRIGGER IF EXISTS update_custody_project_info")

                # Create trigger only for existing columns
                if has_project_column and not has_client_column:
                    new_custody_trigger = """
                    CREATE TRIGGER update_custody_project_info
                    AFTER UPDATE ON المشاريع
                    FOR EACH ROW
                    BEGIN
                        UPDATE المقاولات_العهد
                        SET اسم_المشروع = NEW.اسم_المشروع
                        WHERE معرف_المشروع = NEW.id;
                    END
                    """
                    cursor.execute(new_custody_trigger)
                    print("✅ Recreated custody trigger with اسم_المشروع only")
                elif has_client_column and not has_project_column:
                    new_custody_trigger = """
                    CREATE TRIGGER update_custody_project_info
                    AFTER UPDATE ON المشاريع
                    FOR EACH ROW
                    BEGIN
                        DECLARE client_name VARCHAR(255);
                        SELECT اسم_العميل INTO client_name
                        FROM العملاء
                        WHERE id = NEW.معرف_العميل;

                        UPDATE المقاولات_العهد
                        SET اسم_العميل = client_name
                        WHERE معرف_المشروع = NEW.id;
                    END
                    """
                    cursor.execute(new_custody_trigger)
                    print("✅ Recreated custody trigger with اسم_العميل only")
            else:
                print("📋 Both columns exist - trigger should work correctly")
        
        # Commit changes
        conn.commit()
        print("💾 Changes committed successfully")
        
        # Test the fix by checking if we can insert a test payment
        print("\n🧪 Testing payment insertion...")
        test_insert = """
            INSERT INTO المشاريع_المدفوعات 
            (معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, خصم, المستلم, المستخدم) 
            VALUES (1, 1, 100, 'test payment', CURDATE(), 'نقدي', 0, 'test', 'النظام')
        """
        
        try:
            cursor.execute(test_insert)
            test_payment_id = cursor.lastrowid
            print(f"✅ Test payment inserted successfully with ID: {test_payment_id}")
            
            # Clean up test payment
            cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (test_payment_id,))
            print("🧹 Test payment cleaned up")
            
        except mysql.connector.Error as e:
            print(f"❌ Test payment failed: {e}")
            print("   This might be due to foreign key constraints - check if project ID 1 exists")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 Trigger fix completed successfully!")
        print("   You can now try saving payments in دفعات_المشروع.py")
        
    except ImportError:
        print("❌ Error: Could not import database connection details from for_all.py")
        print("   Make sure for_all.py exists and contains host, user, password variables")
        
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🔧 MySQL Trigger Fix Script")
    print("=" * 50)
    fix_trigger_error()
